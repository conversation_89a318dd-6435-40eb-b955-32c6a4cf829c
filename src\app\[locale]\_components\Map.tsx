import { GoogleMapsEmbed } from "@next/third-parties/google";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const Map = ({ className }: Props) => {
  return (
    <section className={cn("w-full bg-background", className)}>
      <div className="container-x">
        <div className="overflow-hidden rounded-lg border border-border shadow-sm">
          <GoogleMapsEmbed
            apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ""}
            height={400}
            width="100%"
            mode="place"
            q="Rose+Palhares+Ready+to+Wear"
            zoom={15}
            maptype="roadmap"
            loading="lazy"
            allowfullscreen={false}
            style="border: none; display: block;"
          />
        </div>
      </div>
    </section>
  );
};

export default Map;
