import { LOCALES, routing } from "@/i18n/routing";
import { getTranslations, setRequestLocale } from "next-intl/server";
import HeroSlides from "./_components/HeroSlides";
import Map from "./_components/Map";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

const HomePage = async ({
  params,
}: {
  params: Promise<{ locale: (typeof LOCALES)[number] }>;
}) => {
  const t = await getTranslations("common");
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <main className="relative">
      <section className="">
        <div className="bg-primary/50 absolute inset-x-0 top-0 z-10 flex justify-center p-4 text-base font-light tracking-wider text-white uppercase sm:text-xl">
          {t("newsletter")}
        </div>
        <HeroSlides />
      </section>

      <Map className="py-20" />
    </main>
  );
};

export default HomePage;
